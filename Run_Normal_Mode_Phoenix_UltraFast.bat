@echo off
title VolvoFlashWR - Ultra Fast Phoenix APCI
color 0A

echo ===============================================================================
echo                    VolvoFlashWR - Ultra Fast Phoenix APCI
echo ===============================================================================
echo Ultra fast starting VolvoFlashWR application in Normal Mode...

REM Clear any existing environment variables first
set USE_DUMMY_IMPLEMENTATIONS=
set VERBOSE_LOGGING=
set LOG_LEVEL=
set SAFE_MODE=
set DEMO_MODE=
set PHOENIX_VOCOM_ENABLED=

REM Set environment variables for ultra-fast normal mode with Phoenix APCI
set USE_DUMMY_IMPLEMENTATIONS=false
set VERBOSE_LOGGING=false
set LOG_LEVEL=Warning
set PHOENIX_VOCOM_ENABLED=true
set PHOENIX_DIAG_PATH=C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
set FAST_STARTUP=true
set SKIP_DEVICE_SCAN=true
set REDUCE_RETRY_DELAYS=true
set SKIP_PTT_CHECK=true

echo === Ultra Fast Configuration ===
echo USE_DUMMY_IMPLEMENTATIONS=%USE_DUMMY_IMPLEMENTATIONS%
echo PHOENIX_VOCOM_ENABLED=%PHOENIX_VOCOM_ENABLED%
echo LOG_LEVEL=%LOG_LEVEL%
echo FAST_STARTUP=%FAST_STARTUP%
echo SKIP_DEVICE_SCAN=%SKIP_DEVICE_SCAN%
echo REDUCE_RETRY_DELAYS=%REDUCE_RETRY_DELAYS%
echo SKIP_PTT_CHECK=%SKIP_PTT_CHECK%

echo.
echo === Ultra Fast Mode ===
echo Skipping all non-essential checks for maximum speed...

REM Change to the output directory
cd /d "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64"

REM Quick check if executable exists
if not exist "VolvoFlashWR.Launcher.exe" (
    echo ERROR: VolvoFlashWR.Launcher.exe not found!
    echo Please run Run_Normal_Mode_Phoenix.bat first to build the application.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

echo === Starting Application ===
echo Starting VolvoFlashWR Launcher in Ultra Fast Mode...

REM Start the application in the background for fastest startup
start "" "VolvoFlashWR.Launcher.exe" --mode=Normal

REM Return to original directory
cd /d "%~dp0"

echo.
echo Application launched successfully!
echo The VolvoFlashWR application is now running in Ultra Fast Phoenix APCI mode.
echo Note: Device scanning and PTT checks are disabled for maximum speed.
echo Use the regular batch file if you need full functionality.
echo This window will close automatically in 2 seconds...

REM Auto-close after 2 seconds
timeout /t 2 >nul
